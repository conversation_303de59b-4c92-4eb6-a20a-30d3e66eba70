// @ts-check

/**
 * Examples Reference Tool
 * 
 * This tool provides access to the examples.xml file for quality checking and
 * translation guidance. It helps ensure consistency with established translation
 * patterns and provides examples of good translations.
 * 
 * Features:
 * - Parses and indexes examples.xml content
 * - Finds relevant examples for current translation context
 * - Provides translation pattern matching
 * - Suggests improvements based on example patterns
 */

import Anthropic from '@anthropic-ai/sdk';
import fs from 'fs';
import path from 'path';
import { parseStringPromise } from 'xml2js';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  RED: '\x1b[31m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  INFO: '\x1b[36m',
  DEBUG: '\x1b[90m',
  EXAMPLES: '\x1b[34m\x1b[1m'
};

export class ExamplesReference {
  constructor(options = {}) {
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });
    
    this.model = options.model || "claude-sonnet-4-20250514";
    this.examplesPath = options.examplesPath || 'app/2translate/examples.xml';
    this.enableExampleLookup = options.enableExampleLookup !== false;
    this.maxRelevantExamples = options.maxRelevantExamples || 5;
    
    // Cache for parsed examples
    this.examples = [];
    this.examplesIndex = new Map();
    
    this.loadExamples();
    
    console.log(`${COLORS.SUCCESS}📚 [ExamplesReference] Initialized with ${this.examples.length} examples${COLORS.RESET}`);
  }

  /**
   * Load and parse examples from XML file
   */
  async loadExamples() {
    try {
      if (!fs.existsSync(this.examplesPath)) {
        console.warn(`${COLORS.WARNING}⚠️  [ExamplesReference] Examples file not found: ${this.examplesPath}${COLORS.RESET}`);
        return;
      }

      const xmlContent = fs.readFileSync(this.examplesPath, 'utf8');
      const parsedXml = await parseStringPromise(xmlContent);
      
      if (parsedXml.examples && parsedXml.examples.example) {
        this.examples = parsedXml.examples.example.map((example, index) => ({
          id: index,
          englishSource: example.English_Source?.[0] || '',
          idealOutput: example.ideal_output?.[0] || '',
          // Extract keywords for indexing
          keywords: this.extractKeywords(example.English_Source?.[0] || ''),
          // Calculate complexity score
          complexity: this.calculateComplexity(example.English_Source?.[0] || '')
        }));

        // Build search index
        this.buildSearchIndex();
        
        console.log(`${COLORS.SUCCESS}📖 [ExamplesReference] Loaded ${this.examples.length} examples from XML${COLORS.RESET}`);
      }
      
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [ExamplesReference] Failed to load examples: ${error.message}${COLORS.RESET}`);
    }
  }

  /**
   * Build search index for faster example lookup
   */
  buildSearchIndex() {
    this.examplesIndex.clear();
    
    this.examples.forEach((example, index) => {
      // Index by keywords
      example.keywords.forEach(keyword => {
        if (!this.examplesIndex.has(keyword)) {
          this.examplesIndex.set(keyword, []);
        }
        this.examplesIndex.get(keyword).push(index);
      });
      
      // Index by phrases (2-3 word combinations)
      const words = example.englishSource.toLowerCase().split(/\s+/);
      for (let i = 0; i < words.length - 1; i++) {
        const phrase = words.slice(i, i + 2).join(' ');
        if (!this.examplesIndex.has(phrase)) {
          this.examplesIndex.set(phrase, []);
        }
        this.examplesIndex.get(phrase).push(index);
      }
    });
  }

  /**
   * Extract keywords from English text for indexing
   * @param {string} text - English text
   * @returns {Array<string>} - Array of keywords
   */
  extractKeywords(text) {
    // Remove punctuation and convert to lowercase
    const cleanText = text.toLowerCase().replace(/[^\w\s]/g, ' ');
    const words = cleanText.split(/\s+/).filter(word => word.length > 2);
    
    // Filter out common words
    const stopWords = new Set(['the', 'and', 'but', 'for', 'are', 'with', 'his', 'her', 'this', 'that', 'you', 'have', 'will', 'was', 'were', 'been', 'said', 'what', 'when', 'where', 'who', 'why', 'how']);
    
    return words.filter(word => !stopWords.has(word));
  }

  /**
   * Calculate complexity score for an example
   * @param {string} text - English text
   * @returns {number} - Complexity score (0-1)
   */
  calculateComplexity(text) {
    let score = 0;
    
    // Length factor
    score += Math.min(text.length / 100, 0.3);
    
    // Punctuation complexity
    const punctuationCount = (text.match(/[!?;:,]/g) || []).length;
    score += Math.min(punctuationCount / 5, 0.2);
    
    // Word complexity
    const complexWords = text.split(/\s+/).filter(word => word.length > 6).length;
    score += Math.min(complexWords / 10, 0.3);
    
    // Special characters (quotes, parentheses, etc.)
    const specialChars = (text.match(/["'(){}[\]]/g) || []).length;
    score += Math.min(specialChars / 5, 0.2);
    
    return Math.min(score, 1);
  }

  /**
   * Find relevant examples for translation guidance
   * @param {string} originalText - Original English text to translate
   * @param {string} currentTranslation - Current Polish translation (optional)
   * @param {Object} context - Translation context
   * @returns {Promise<Object>} - Examples and guidance
   */
  async findRelevantExamples(originalText, currentTranslation = '', context = {}) {
    try {
      console.log(`${COLORS.EXAMPLES}📚 [ExamplesReference] Finding relevant examples...${COLORS.RESET}`);
      
      if (!this.enableExampleLookup || this.examples.length === 0) {
        return {
          relevantExamples: [],
          guidance: null,
          hasExamples: false
        };
      }

      // Find examples using multiple strategies
      const relevantExamples = this.findExamplesByRelevance(originalText);
      
      if (relevantExamples.length > 0) {
        console.log(`${COLORS.INFO}📖 [ExamplesReference] Found ${relevantExamples.length} relevant examples${COLORS.RESET}`);
        
        // Generate guidance using Claude 4
        const guidance = await this.generateGuidanceFromExamples(
          originalText, 
          currentTranslation, 
          relevantExamples, 
          context
        );
        
        return {
          relevantExamples: relevantExamples.slice(0, this.maxRelevantExamples),
          guidance: guidance,
          hasExamples: true
        };
      } else {
        console.log(`${COLORS.DEBUG}🔍 [ExamplesReference] No relevant examples found${COLORS.RESET}`);
        return {
          relevantExamples: [],
          guidance: null,
          hasExamples: false
        };
      }
      
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [ExamplesReference] Error finding examples: ${error.message}${COLORS.RESET}`);
      return {
        relevantExamples: [],
        guidance: null,
        hasExamples: false,
        error: error.message
      };
    }
  }

  /**
   * Find examples by relevance using multiple strategies
   * @param {string} originalText - Original English text
   * @returns {Array} - Array of relevant examples with scores
   */
  findExamplesByRelevance(originalText) {
    const relevanceScores = new Map();
    const keywords = this.extractKeywords(originalText);
    const originalWords = originalText.toLowerCase().split(/\s+/);
    
    // Strategy 1: Keyword matching
    keywords.forEach(keyword => {
      if (this.examplesIndex.has(keyword)) {
        this.examplesIndex.get(keyword).forEach(exampleIndex => {
          const currentScore = relevanceScores.get(exampleIndex) || 0;
          relevanceScores.set(exampleIndex, currentScore + 0.3);
        });
      }
    });
    
    // Strategy 2: Phrase matching
    for (let i = 0; i < originalWords.length - 1; i++) {
      const phrase = originalWords.slice(i, i + 2).join(' ');
      if (this.examplesIndex.has(phrase)) {
        this.examplesIndex.get(phrase).forEach(exampleIndex => {
          const currentScore = relevanceScores.get(exampleIndex) || 0;
          relevanceScores.set(exampleIndex, currentScore + 0.5);
        });
      }
    }
    
    // Strategy 3: Similarity scoring
    this.examples.forEach((example, index) => {
      const similarity = this.calculateTextSimilarity(originalText, example.englishSource);
      if (similarity > 0.2) {
        const currentScore = relevanceScores.get(index) || 0;
        relevanceScores.set(index, currentScore + similarity);
      }
    });
    
    // Convert to array and sort by relevance
    const relevantExamples = Array.from(relevanceScores.entries())
      .map(([index, score]) => ({
        ...this.examples[index],
        relevanceScore: score
      }))
      .filter(example => example.relevanceScore > 0.3)
      .sort((a, b) => b.relevanceScore - a.relevanceScore);
    
    return relevantExamples;
  }

  /**
   * Calculate text similarity between two strings
   * @param {string} text1 - First text
   * @param {string} text2 - Second text
   * @returns {number} - Similarity score (0-1)
   */
  calculateTextSimilarity(text1, text2) {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));

    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  /**
   * Generate guidance from relevant examples using Claude 4
   * @param {string} originalText - Original English text
   * @param {string} currentTranslation - Current Polish translation
   * @param {Array} relevantExamples - Array of relevant examples
   * @param {Object} context - Translation context
   * @returns {Promise<Object>} - Generated guidance
   */
  async generateGuidanceFromExamples(originalText, currentTranslation, relevantExamples, context) {
    const tools = [
      {
        name: "provide_translation_guidance",
        description: "Provide translation guidance based on relevant examples",
        input_schema: {
          type: "object",
          properties: {
            guidance_type: {
              type: "string",
              enum: ["improvement", "validation", "pattern_suggestion", "cultural_adaptation"],
              description: "Type of guidance being provided"
            },
            key_insights: {
              type: "array",
              items: { type: "string" },
              description: "Key insights from the examples"
            },
            suggested_improvements: {
              type: "array",
              items: { type: "string" },
              description: "Specific improvements suggested based on examples"
            },
            pattern_matches: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  example_english: { type: "string" },
                  example_polish: { type: "string" },
                  relevance: { type: "string" }
                }
              },
              description: "Examples that match current translation patterns"
            },
            confidence: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Confidence in the guidance (0-1)"
            }
          },
          required: ["guidance_type", "key_insights", "confidence"]
        }
      }
    ];

    const prompt = this.buildGuidancePrompt(originalText, currentTranslation, relevantExamples, context);

    const response = await this.anthropic.messages.create({
      model: this.model,
      max_tokens: 2048,
      temperature: 0.4,
      messages: [{ role: 'user', content: prompt }],
      tools: tools,
      tool_choice: { type: "tool", name: "provide_translation_guidance" }
    });

    // Process the response
    for (const content of response.content) {
      if (content.type === 'tool_use' && content.name === 'provide_translation_guidance') {
        return {
          type: content.input.guidance_type,
          insights: content.input.key_insights || [],
          improvements: content.input.suggested_improvements || [],
          patternMatches: content.input.pattern_matches || [],
          confidence: content.input.confidence
        };
      }
    }

    // Fallback if tool use fails
    return {
      type: "validation",
      insights: ["Examples analysis completed"],
      improvements: [],
      patternMatches: [],
      confidence: 0.5
    };
  }

  /**
   * Build guidance prompt for Claude 4
   * @param {string} originalText - Original English text
   * @param {string} currentTranslation - Current Polish translation
   * @param {Array} relevantExamples - Relevant examples
   * @param {Object} context - Translation context
   * @returns {string} - Guidance prompt
   */
  buildGuidancePrompt(originalText, currentTranslation, relevantExamples, context) {
    const examplesText = relevantExamples.map((example, index) =>
      `Example ${index + 1} (relevance: ${example.relevanceScore.toFixed(2)}):
English: ${example.englishSource}
Polish: ${example.idealOutput}`
    ).join('\n\n');

    return `You are an expert Polish translator analyzing translation patterns from established examples. Use the provided examples to guide and improve the current translation.

CURRENT TRANSLATION TASK:
Original English: ${originalText}
Current Polish: ${currentTranslation || 'Not provided yet'}

CONTEXT:
- Anime: ${context.animeTitle || 'Unknown'}
- Episode: ${context.episode || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}

RELEVANT EXAMPLES FROM DATABASE:
${examplesText}

Analysis tasks:
1. Compare the current translation (if provided) with the patterns shown in examples
2. Identify translation techniques and patterns used in the examples
3. Look for similar grammatical structures, cultural adaptations, or stylistic choices
4. Suggest improvements based on the example patterns
5. Note any cultural or linguistic insights from the examples

Focus on:
- Polish grammar and sentence structure patterns
- Cultural adaptation techniques
- Honorific and formality handling
- Idiomatic expression translations
- Character voice consistency
- Punctuation and formatting patterns

Use the provide_translation_guidance tool to deliver your analysis and recommendations.`;
  }

  /**
   * Get examples by category or pattern
   * @param {string} category - Category to search for
   * @returns {Array} - Filtered examples
   */
  getExamplesByCategory(category) {
    const categoryKeywords = {
      'honorifics': ['san', 'chan', 'kun', 'sama', 'senpai'],
      'questions': ['?', 'what', 'how', 'why', 'when', 'where'],
      'emotions': ['happy', 'sad', 'angry', 'excited', 'worried', 'surprised'],
      'actions': ['run', 'walk', 'fight', 'attack', 'defend', 'move'],
      'dialogue': ['said', 'asked', 'replied', 'shouted', 'whispered'],
      'formal': ['please', 'thank you', 'excuse me', 'sir', 'madam'],
      'informal': ['hey', 'yeah', 'nope', 'cool', 'awesome']
    };

    const keywords = categoryKeywords[category.toLowerCase()] || [];
    if (keywords.length === 0) return [];

    return this.examples.filter(example =>
      keywords.some(keyword =>
        example.englishSource.toLowerCase().includes(keyword) ||
        example.idealOutput.toLowerCase().includes(keyword)
      )
    );
  }

  /**
   * Reload examples from file (useful for updates)
   */
  async reloadExamples() {
    console.log(`${COLORS.INFO}🔄 [ExamplesReference] Reloading examples...${COLORS.RESET}`);
    await this.loadExamples();
  }

  /**
   * Get statistics about loaded examples
   * @returns {Object} - Examples statistics
   */
  getStatistics() {
    const complexityDistribution = {
      simple: this.examples.filter(e => e.complexity < 0.3).length,
      medium: this.examples.filter(e => e.complexity >= 0.3 && e.complexity < 0.7).length,
      complex: this.examples.filter(e => e.complexity >= 0.7).length
    };

    return {
      totalExamples: this.examples.length,
      indexedTerms: this.examplesIndex.size,
      complexityDistribution: complexityDistribution,
      averageComplexity: this.examples.reduce((sum, e) => sum + e.complexity, 0) / this.examples.length
    };
  }
}
