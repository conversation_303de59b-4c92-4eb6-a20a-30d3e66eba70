# Splendour Cafe Bot 2 - Enhanced Anime Translation System

An advanced anime subtitle translation system powered by Claude 3.5 Sonnet for translation and Claude 4 Sonnet for specialized tools, featuring intelligent scene detection, second language validation, examples-based learning, and persistent metadata management.

## 🚀 New Features (Latest Update)

### Tool-Based Translation Architecture
The system now uses a sophisticated tool-based approach with the following enhancements:

- **🔍 Second Language Source Validation**: Automatically validates translations using alternative language sources when quality seems questionable
- **📚 Examples.xml Reference System**: Leverages a database of translation examples for consistent, high-quality outputs
- **💾 Anime Metadata Persistence**: Maintains character information, terminology, and translation patterns across episodes
- **🎯 Intelligent Scene Detection**: Advanced scene boundary detection for better context management
- **🖼️ Visual Context Integration**: Screenshot analysis for visual elements (when needed)
- **✨ Polish Grammar Enhancement**: Specialized Polish language corrections with punctuation rules

### Complete Application Architecture

For a detailed view of the entire application workflow including edge cases, conditionals, and tool interactions, see the [Complete Application Flow Diagram](#complete-application-flow-diagram) below.

## 🔄 Complete Application Flow Diagram

The following comprehensive diagram shows the entire application workflow with all edge cases, conditionals, error handling, and tool interactions:

```mermaid
graph TD
    %% RSS Download Phase
    A[🚀 Start: RSS Download] --> B{Check isRunning.txt}
    B -->|Already Running| C[⏸️ Skip Execution]
    B -->|Not Running| D[📡 Fetch RSS Feed from nyaa.si]
    D --> E{RSS Fetch Success?}
    E -->|Failed| F[🔄 Retry up to 3 times]
    F -->|Max Retries| G[❌ Exit with Error]
    F -->|Retry| D
    E -->|Success| H[📋 Parse RSS XML]
    H --> I[🔍 Filter by Whitelist]
    I --> J{ToonsHub Format?}
    J -->|Yes| K{H.265 Format?}
    K -->|Yes| L[⏭️ Skip H.265 Release]
    K -->|No H.264| M[📥 Download Torrent]
    J -->|No| M
    M --> N[📊 Progress Tracking]
    N --> O[🏷️ File Renaming]
    O --> P[📝 Update Processed Lists]
    P --> Q[🧹 Start Clear Process]

    %% Clear Phase
    Q --> R[🎬 Clear.js: Extract Subtitles]
    R --> S[📁 Scan Downloads for .mkv]
    S --> T{MKV Files Found?}
    T -->|No| U[⚠️ No Files to Process]
    T -->|Yes| V[🔄 Process Each File]
    V --> W[🎯 Extract English Subtitles]
    W --> X[🇫🇷 Extract French Subtitles]
    X --> Y{French Extraction Success?}
    Y -->|Failed| Z[⚠️ Continue without French]
    Y -->|Success| AA[✅ Dual Language Ready]
    Z --> BB[🧹 Clean Dialogue Lines]
    AA --> BB
    BB --> CC[👥 Actor Prediction]
    CC --> DD[💾 Save to extracted/]
    DD --> EE[🔄 Next File or Continue]
    EE --> FF[🌟 Start Translation Process]

    %% Translation Phase
    FF --> GG[🤖 Claude4 Translator Init]
    GG --> HH[📊 Load Configuration]
    HH --> II[🎭 Fetch Anime Metadata]
    II --> JJ{Metadata Success?}
    JJ -->|Failed| KK[⚠️ Continue without Metadata]
    JJ -->|Success| LL[📚 Initialize Context Manager]
    KK --> MM[🎬 Scene Detection]
    LL --> MM
    MM --> NN[🔍 Analyze Scene Boundaries]
    NN --> OO[🎯 Process Each Scene]
    OO --> PP{Screenshot Needed?}
    PP -->|Yes| QQ[📸 Capture Frame]
    PP -->|No| RR[🔧 Translate with Tools]
    QQ --> RR
    RR --> SS[🛠️ Tool: translate_with_context]
    SS --> TT{Quality Assessment}
    TT -->|Poor Quality| UU[🔍 Second Language Validator]
    TT -->|Good Quality| VV[📚 Examples Reference]
    UU --> WW[🇫🇷 Cross-validate with French]
    WW --> XX{Validation Improves?}
    XX -->|Yes| YY[✨ Apply Improved Translation]
    XX -->|No| ZZ[⚠️ Flag for Review]
    VV --> AAA[🔍 Pattern Matching]
    AAA --> BBB[📖 Find Relevant Examples]
    BBB --> YY
    YY --> CCC[🇵🇱 Polish Grammar Correction]
    ZZ --> CCC
    CCC --> DDD{High Priority Punctuation?}
    DDD -->|Yes| EEE[✅ Auto-apply Fixes]
    DDD -->|No| FFF[📝 Suggest Corrections]
    EEE --> GGG[💾 Update Context Manager]
    FFF --> GGG
    GGG --> HHH[📊 Metadata Persistence]
    HHH --> III{More Scenes?}
    III -->|Yes| OO
    III -->|No| JJJ[💾 Save Episode Data]
    JJJ --> KKK[🔄 Next File or Continue]
    KKK --> LLL[🎯 Start Replace Process]

    %% Replace Phase
    LLL --> MMM[🎭 Separate Actors]
    MMM --> NNN[📁 Process withActors files]
    NNN --> OOO[✂️ Remove Actor Prefixes]
    OOO --> PPP[💾 Save to clean/]
    PPP --> QQQ[🔄 Apply Translation]
    QQQ --> RRR[📁 Match .ass with .txt]
    RRR --> SSS{Files Match?}
    SSS -->|No| TTT[⚠️ Skip Unmatched]
    SSS -->|Yes| UUU[🔄 Replace Dialogue Lines]
    UUU --> VVV[⏰ Preserve Timing]
    VVV --> WWW[👥 Preserve Actors]
    WWW --> XXX[💾 Save Final .ass]
    XXX --> YYY[🧹 Cleanup Directories]
    YYY --> ZZZ[✅ Process Complete]

    %% Styling
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef tool fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px

    class A,ZZZ startEnd
    class D,H,I,M,R,W,X,GG,MM,RR,MMM,QQQ process
    class B,E,J,K,T,Y,JJ,PP,TT,XX,DDD,III,SSS decision
    class G,U error
    class YY,EEE,XXX success
    class SS,UU,VV,CCC tool
```

### Key Components Explained

#### 🔄 **Edge Cases & Conditionals**
- **Concurrent Execution Prevention**: `isRunning.txt` prevents multiple instances
- **Format-Specific Handling**: ToonsHub H.265 files are automatically skipped
- **Graceful Degradation**: French subtitle extraction failure doesn't stop processing
- **Quality-Based Routing**: Poor translations trigger second language validation
- **File Matching**: Unmatched subtitle files are safely skipped

#### 🛠️ **Tool Integration Points**
- **Scene Detection**: Intelligent boundary analysis using timing, speakers, and content
- **Second Language Validator**: Cross-references French subtitles for quality assurance
- **Examples Reference**: Pattern matching against examples.xml database
- **Polish Grammar Correction**: High-priority punctuation fixes with auto-application
- **Metadata Persistence**: Character and terminology tracking across episodes

#### ⚠️ **Error Handling & Recovery**
- **RSS Feed Failures**: 3-retry mechanism with exponential backoff
- **Missing Files**: Graceful handling of missing MKV or subtitle files
- **Translation Errors**: Fallback mechanisms and quality flagging
- **Critical vs Non-Critical**: Different handling strategies based on error severity

## 📁 Project Structure

```
app/
├── 0rss/              # RSS feed processing and torrent downloads
├── 1clear/            # Subtitle extraction from MKV files
│   └── clear.js       # Enhanced with French subtitle support
├── 2translate/        # Core translation system
│   ├── tools/         # New tool-based architecture
│   │   ├── second-language-validator.js
│   │   ├── examples-reference.js
│   │   ├── metadata-persistence.js
│   │   ├── scene-detector.js
│   │   ├── screenshot-tool.js
│   │   └── correction-tool.js
│   ├── claude4-translator.js  # Main translator with tool integration
│   ├── translate.js           # Updated workflow
│   ├── config.js             # Cleaned configuration
│   └── examples.xml          # Translation examples database
├── 3replace/          # Final subtitle processing
└── metadata/          # Persistent anime metadata storage
    ├── anime/         # Anime-specific metadata
    ├── characters/    # Character information
    ├── terminology/   # Translation terminology
    └── patterns/      # Translation patterns
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ 
- FFmpeg (for video processing)
- Anthropic API Key (Claude 4 Sonnet access)

### Installation
```bash
npm install
```

### Environment Configuration
Create a `.env` file:
```env
ANTHROPIC_API_KEY=your_claude_4_api_key_here
DISCORD_WEBHOOK_URL=your_discord_webhook_url
```

## 🔧 Configuration

The system is configured through `app/2translate/config.js`:

### Key Configuration Options
```javascript
{
  // Core Translation (Claude 3.5 Sonnet for translation tasks)
  model: "claude-3-5-sonnet-20241022",
  temperature: 0.7,
  maxTokens: 8192,

  // New Tool Features
  secondLanguageValidation: {
    enableValidation: true,
    validationThreshold: 0.6
  },
  
  examplesReference: {
    enableExampleLookup: true,
    maxRelevantExamples: 5
  },
  
  metadataPersistence: {
    enablePersistence: true,
    autoSave: true
  }
}
```

## 🚀 Usage Guide

### Complete Workflow Overview

The application follows a **4-phase pipeline** with automatic progression between phases:

1. **📡 RSS Download Phase** → 2. **🎬 Subtitle Extraction Phase** → 3. **🤖 Translation Phase** → 4. **🔄 Final Processing Phase**

### Phase-by-Phase Execution

#### **Phase 1: RSS Download & Content Acquisition**
```bash
# Automatic RSS monitoring (runs every 5 minutes via cron)
node app/0rss/download.js

# Manual torrent download
node app/0rss/download.js "magnet:?xt=urn:btih:..."
```

**What happens:**
- 📡 Fetches RSS feed from nyaa.si
- 🔍 Filters by whitelist (`whitelist.txt`, `whitelist_other.txt`)
- ⚠️ Skips ToonsHub H.265 releases (only downloads H.264)
- 📥 Downloads torrents with progress tracking
- 🏷️ Renames files and updates processed lists
- ▶️ **Automatically triggers Phase 2**

#### **Phase 2: Subtitle Extraction & Cleaning**
```bash
# Automatically called by Phase 1, or run manually:
node app/1clear/clear.js
```

**What happens:**
- 📁 Scans `app/0rss/downloads/` for .mkv files
- 🎯 Extracts English subtitles (primary)
- 🇫🇷 Extracts French subtitles (for validation)
- 🧹 Cleans dialogue lines (removes timing, keeps content)
- 👥 Predicts missing actor names
- 💾 Saves cleaned files to `app/1clear/extracted/`
- ▶️ **Automatically triggers Phase 3**

#### **Phase 3: AI-Powered Translation**
```bash
# Automatically called by Phase 2, or run manually:
node app/2translate/translate.js
```

**What happens:**
- 🤖 Initializes Claude 4 Sonnet translator
- 📊 Loads configuration and anime metadata
- 🎬 Performs intelligent scene detection
- 🔧 Translates each scene using tool-based approach:
  - 🛠️ `translate_with_context` tool
  - 🔍 Second language validation (if quality is poor)
  - 📚 Examples reference lookup
  - 🇵🇱 Polish grammar correction
- 💾 Persists metadata for future episodes
- 💾 Saves translations to `app/2translate/toTranslate/`
- ▶️ **Automatically triggers Phase 4**

#### **Phase 4: Final Processing & Output**
```bash
# Automatically called by Phase 3, or run manually:
node app/3replace/1separateActors.js
# Then automatically:
node app/3replace/2applyTranslation.js
```

**What happens:**
- 🎭 Separates actor names from dialogue
- 💾 Saves clean dialogue to `app/3replace/clean/`
- 🔄 Matches .ass files with .txt translations
- ⏰ Preserves original timing and formatting
- 👥 Maintains actor assignments
- 💾 Generates final .ass files
- 🧹 Cleans up temporary directories

### Manual Execution Options

#### **Full Pipeline (Recommended)**
```bash
# Start from RSS download - everything runs automatically
node app/0rss/download.js
```

#### **Individual Phases**
```bash
# Phase 1: Download only
node app/0rss/download.js

# Phase 2: Extract subtitles (requires .mkv files in downloads/)
node app/1clear/clear.js

# Phase 3: Translate (requires files in extracted/)
node app/2translate/translate.js

# Phase 4a: Separate actors (requires files in toTranslate/)
node app/3replace/1separateActors.js

# Phase 4b: Apply translations (requires files in withActors/)
node app/3replace/2applyTranslation.js
```

#### **Testing & Validation**
```bash
# Comprehensive production test (first 60 lines only)
node app/production-test.js

# Utility scripts for karaoke timing
node app/karaokeNormalize.js  # Normalize timecodes to start at 00:00:00
node app/karaokeOffset.js     # Add time offset to all timecodes
```

### Advanced Configuration

#### **Whitelist Management**
- **`app/0rss/whitelist.txt`**: Standard anime titles
- **`app/0rss/whitelist_other.txt`**: ToonsHub-specific titles
- Add anime titles (one per line) to automatically download new episodes

#### **Translation Examples**
- **`app/2translate/examples.xml`**: Translation pattern database
- Add new examples to improve translation consistency
- Format: `<English_Source>` → `<ideal_output>`

#### **Configuration Tuning**
- **`app/2translate/config.js`**: Main configuration file
- Adjust Claude 4 parameters, scene detection settings, tool behavior
- Enable/disable features like screenshots, correction, metadata persistence

### Monitoring & Logs

#### **Real-time Monitoring**
- **Colored terminal output** shows progress and status
- **Discord webhook** notifications (if configured)
- **Progress bars** for downloads and processing

#### **Log Files**
- **`app/logs/info.log`**: General information and progress
- **`app/logs/error.log`**: Error messages and warnings
- **`app/logs/exception.log`**: Uncaught exceptions
- **`app/logs/rejection.log`**: Unhandled promise rejections

#### **State Files**
- **`app/isRunning.txt`**: Prevents concurrent execution
- **`app/0rss/lastId.txt`**: Tracks last processed RSS entry
- **`app/0rss/processedTitles.txt`**: List of processed anime titles
- **`app/0rss/processed_ids.txt`**: List of processed RSS IDs

### Troubleshooting

#### **Common Issues**
1. **"Another instance running"**: Wait for current process to finish or delete `app/isRunning.txt`
2. **"No MKV files found"**: Ensure files are in `app/0rss/downloads/`
3. **"French extraction failed"**: Normal - processing continues with English only
4. **"Translation quality poor"**: Second language validation will attempt improvement

#### **Error Recovery**
- **Non-critical errors**: Logged and processing continues
- **Critical errors**: Process stops with detailed error message
- **Network failures**: Automatic retry with exponential backoff
- **File corruption**: Graceful skipping with warning

## 🔍 New Translation Features

### Second Language Validation
- Automatically detects potentially problematic translations
- Cross-references with French subtitle sources
- Provides alternative suggestions when quality is questionable
- Maintains confidence scoring for validation decisions

### Examples-Based Learning
- Leverages `examples.xml` database for translation patterns
- Finds relevant examples based on context and content similarity
- Provides guidance for consistent terminology and style
- Supports categorized lookups (honorifics, emotions, actions, etc.)

### Metadata Persistence
- **Anime Metadata**: Title, genres, episode count, translation statistics
- **Character Database**: Names, personalities, speech patterns, gender information
- **Terminology Management**: Consistent translation of recurring terms
- **Translation Patterns**: Series-specific translation preferences

### Enhanced Polish Grammar
- Specialized punctuation rules with high priority for comma placement
- Automatic correction of missing or misplaced punctuation
- Gender agreement validation
- Case declension corrections
- Cultural adaptation for Polish audiences

## 📊 Quality Improvements

The enhanced system provides significant quality improvements:

### Translation Quality
- **Consistency**: Metadata persistence ensures character and terminology consistency across episodes
- **Accuracy**: Second language validation catches translation errors and provides alternatives
- **Naturalness**: Examples database provides proven translation patterns and cultural adaptations
- **Polish Language**: Specialized grammar rules for authentic Polish subtitles with proper punctuation
- **Context Awareness**: Scene detection maintains narrative flow and character development

### Technical Enhancements
- **Dual Language Support**: Extracts both English and French subtitles for cross-validation
- **File Format Compatibility**: Supports Erai-raws and ToonsHub MKV file formats
- **Graceful Degradation**: Continues processing when French subtitles are unavailable
- **Error Recovery**: Robust error handling with multiple extraction fallback strategies
- **Performance Optimization**: Colored terminal output and detailed progress tracking

### Subtitle Processing
- **MKV Compatibility**: Direct extraction from MKV files without baked-in subtitle issues
- **Multi-track Support**: Handles multiple subtitle tracks with intelligent selection
- **Format Preservation**: Maintains timing and speaker information throughout the pipeline
- **Quality Validation**: Confidence scoring and quality metrics for each translation

## 🔧 Advanced Features

### Visual Context Integration
- Screenshot analysis for visual elements (when needed)
- Note: Not required for MKV files as they don't have baked-in subtitles

### Intelligent Scene Detection
- Timing gap analysis
- Speaker change detection
- Content transition recognition
- Optimal scene boundary identification

### Performance Optimization
- Colored terminal output for better user experience
- Detailed logging and progress tracking
- Error recovery and graceful degradation
- Memory-efficient metadata management

## 📝 Migration Notes

### What's New in This Version

#### Major Features Added
- **🔍 Second Language Validation**: Automatic quality checking using French subtitles
- **📚 Examples Database Integration**: Real-time pattern matching from examples.xml
- **💾 Persistent Metadata System**: Character, terminology, and pattern storage across episodes
- **🇫🇷 French Subtitle Extraction**: Dual language support for enhanced validation
- **✨ Enhanced Polish Grammar**: Specialized punctuation and grammar correction rules

#### Technical Improvements
- **Tool-Based Architecture**: Modular, extensible translation system
- **Colored Terminal Output**: Better user experience with visual feedback
- **Robust Error Handling**: Graceful degradation and recovery mechanisms
- **Performance Optimization**: Efficient metadata management and caching
- **File Format Support**: Enhanced MKV processing for multiple subtitle tracks

### Breaking Changes
- **Removed**: Old fallback translation logic (now uses tool-based approach only)
- **Cleaned**: Unused configuration options (enableFallback, enableParallelProcessing, etc.)
- **Updated**: Translation workflow now requires new tool architecture
- **Enhanced**: Clear.js now extracts both English and French subtitles by default

### Backward Compatibility
- **✅ Subtitle Files**: Existing subtitle files remain fully compatible
- **✅ Configuration**: Automatic migration of valid configuration options
- **✅ Metadata**: Previous translation data can be imported into new metadata system
- **✅ Examples**: Existing examples.xml files work with new reference system

### Migration Steps
1. **Update Dependencies**: Run `npm install` to get xml2js and other new dependencies
2. **Environment Setup**: Ensure ANTHROPIC_API_KEY is configured for Claude 4 Sonnet
3. **Test New Tools**: Run `node app/2translate/test-tools.js` to verify functionality
4. **Gradual Migration**: New features activate automatically, old workflows continue working

## 🤝 Contributing

The system is designed for extensibility:
- Add new tools in `app/2translate/tools/`
- Extend examples database in `examples.xml`
- Customize Polish grammar rules in correction tools
- Add new metadata types in persistence system

## 📄 License

This project is for personal use and educational purposes.
